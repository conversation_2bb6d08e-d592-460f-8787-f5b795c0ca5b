<script setup>
// 无需引入 HelloWorld
</script>

<template>
  <div class="app-container">
    <!-- 固定导航栏 -->
    <nav class="fixed-nav">
      <div class="nav-container">
        <div class="nav-left">
          <router-link to="/" class="nav-logo">
            <img src="/images/miaomiaowangwang.png" alt="Logo" class="nav-logo-img">
            <span class="nav-logo-text">自动作业批改机</span>
          </router-link>
        </div>
        <div class="nav-right">
          <div class="phone-info">
            <div class="phone-icon">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
              </svg>
            </div>
            <span class="phone-number">18939855586</span>
          </div>
          <router-link to="/" class="nav-link">
            主页
            <svg class="nav-arrow" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M6 9l6 6 6-6"/>
            </svg>
          </router-link>
          <router-link to="/product" class="nav-link">
            产品
            <svg class="nav-arrow" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M6 9l6 6 6-6"/>
            </svg>
          </router-link>
          <!-- <router-link to="/education" class="nav-link">教育</router-link> -->
          <router-link to="/price" class="nav-link">
            价格
            <svg class="nav-arrow" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M6 9l6 6 6-6"/>
            </svg>
          </router-link>
          <router-link to="/contact" class="nav-link">
            联系我们
            <svg class="nav-arrow" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M6 9l6 6 6-6"/>
            </svg>
          </router-link>
        </div>
      </div>
    </nav>
    
    <!-- 主内容区域 -->
    <main class="main-content">
      <router-view />
    </main>
    <footer class="icp-footer">
      浙ICP备2024103593号-1 杭州喵喵汪汪科技智能有限公司
    </footer>
  </div>
</template>

<style scoped>
.app-container {
  min-height: 100vh;
  position: relative;
  display: flex;
  flex-direction: column;
}

.fixed-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  padding: 0;
}

.nav-container {
  width: 97%;
  margin: 0;
  padding: 0 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
}

.nav-left {
  display: flex;
  align-items: center;
}

.phone-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.phone-icon {
  width: 32px;
  height: 32px;
  background: #333;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.phone-number {
  font-size: 1rem;
  font-weight: 500;
  color: #333;
  letter-spacing: 0.5px;
}

.nav-logo {
  font-size: 1.5rem;
  font-weight: 700;
  color: rgb(96,166,255);
  text-decoration: none;
  transition: color 0.3s;
  display: flex;
  align-items: center;
  gap: 12px;
}

.nav-logo:hover {
  color: rgb(76,146,235);
}

.nav-logo-img {
  width: 40px;
  height: 40px;
  object-fit: contain;
  transition: transform 0.3s ease;
}

.nav-logo:hover .nav-logo-img {
  transform: scale(1.25);
}

.nav-logo-text {
  font-size: 1.5rem;
  font-weight: 700;
  transition: color 0.3s ease, font-weight 0.3s ease;
}

.nav-logo:hover .nav-logo-text {
  color: rgb(76,146,235);
  font-weight: 900;
}

.nav-right {
  display: flex;
  gap: 30px;
  align-items: center;
}

.nav-link {
  text-decoration: none;
  color: #333;
  font-size: 1rem;
  font-weight: 500;
  padding: 8px 16px;
  border-radius: 6px;
  transition: all 0.3s;
  position: relative;
  display: flex;
  align-items: center;
  gap: 4px;
}

.nav-arrow {
  transition: transform 0.3s ease;
  opacity: 0.7;
}

.nav-link:hover {
  background: rgba(96,166,255, 0.1);
  color: rgb(96,166,255);
}

.nav-link:hover .nav-arrow {
  transform: translateY(2px);
  opacity: 1;
}

.nav-link.router-link-exact-active {
  background: rgb(96,166,255);
  color: white;
}

.nav-link.router-link-exact-active:hover {
  background: rgb(76,146,235);
  color: white;
}

.main-content {
  flex: 1 0 auto;
  padding-top: 70px;
  padding-bottom: 120px; /* 给footer SVG留空间 */
}

@media (max-width: 1056px) {
  .main-content {
    padding-bottom: 20vw;
  }
}

@media (max-width: 600px) {
  .main-content {
    padding-bottom: 24vw;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .nav-container {
    padding: 0 30px;
    height: 60px;
  }

  .phone-icon {
    width: 28px;
    height: 28px;
  }

  .phone-number {
    font-size: 0.9rem;
  }

  .nav-right {
    gap: 15px;
  }

  .nav-link {
    font-size: 0.9rem;
    padding: 6px 12px;
  }

  .nav-arrow {
    width: 10px;
    height: 10px;
  }

  .nav-logo-img {
    width: 32px;
    height: 32px;
  }

  .nav-logo-text {
    font-size: 1.2rem;
  }

  .main-content {
    padding-top: 60px;
  }
}

@media (max-width: 480px) {
  .nav-container {
    padding: 0 20px;
  }

  .phone-info {
    gap: 6px;
  }

  .phone-icon {
    width: 24px;
    height: 24px;
  }

  .phone-icon svg {
    width: 12px;
    height: 12px;
  }

  .phone-number {
    font-size: 0.8rem;
  }

  .nav-right {
    gap: 10px;
  }

  .nav-link {
    font-size: 0.8rem;
    padding: 4px 8px;
  }

  .nav-arrow {
    width: 8px;
    height: 8px;
  }
}

.icp-footer {
  width: 100%;
  text-align: center;
  color: #888;
  font-size: 1rem;
  padding: 16px 0 12px 0;
  background: transparent;
  letter-spacing: 1px;
}
</style>
